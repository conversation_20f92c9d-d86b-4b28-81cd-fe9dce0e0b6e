# Auth Store 修復文檔

## 修復的問題

### 1. 重複變數宣告
**問題**：在文件開頭有重複的變數宣告
```typescript
// 重複的宣告
let wsReconnectTimer: NodeJS.Timeout | null = null;
let wsConnectionState: 'disconnected' | 'connecting' | 'connected' | 'failed' = 'disconnected';
let wsReconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000;
```

**修復**：移除重複的宣告，保留唯一的變數定義

### 2. 不正確的 useRouter 使用
**問題**：在 Pinia store 的 action 中使用 `useRouter()`
```typescript
// 錯誤的使用方式
const router = useRouter();
router.push('/login');
```

**修復**：使用 `window.location.href` 替代
```typescript
// 正確的使用方式
window.location.href = '/login';
```

**原因**：`useRouter()` 只能在 Vue 組件的 setup 函數中使用，不能在 Pinia store 中使用

### 3. 未使用的導入
**問題**：導入了 `useRouter` 但使用方式不正確
```typescript
import { useRouter } from 'vue-router'; // 未正確使用
```

**修復**：移除未使用的導入

## 修復後的文件結構

### 變數宣告（文件頂部）
```typescript
// WebSocket連接
let ws: WebSocket | null = null;

// Token驗證定時器
let tokenValidationTimer: NodeJS.Timeout | null = null;

// WebSocket重連定時器
let wsReconnectTimer: NodeJS.Timeout | null = null;

// WebSocket連接狀態
let wsConnectionState: 'disconnected' | 'connecting' | 'connected' | 'failed' = 'disconnected';

// WebSocket重連次數
let wsReconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000; // 3秒
```

### 導入語句
```typescript
import { defineStore } from 'pinia';
import { Dialog } from 'quasar';
import { LoginResponse, TokenResponse, User } from '@/api/modules/auth';
```

### 跨分頁登出處理
```typescript
// 監聽登出事件
if (event.key === 'auth-logout-sync' && event.newValue === 'true') {
  // ... 清理邏輯 ...
  
  // 跳轉到登入頁面
  window.location.href = '/login';
}
```

## 驗證修復

### 1. TypeScript 編譯檢查
- ✅ 無重複變數宣告錯誤
- ✅ 無未使用導入警告
- ✅ 無類型錯誤

### 2. 運行時檢查
- ✅ WebSocket 連接功能正常
- ✅ 跨分頁同步功能正常
- ✅ 強制登出跳轉功能正常

### 3. 功能測試
- ✅ 登入/登出功能
- ✅ Token 刷新功能
- ✅ WebSocket 重連功能
- ✅ 跨分頁同步功能

## 注意事項

### 1. 路由跳轉
在 Pinia store 中進行路由跳轉時，應該使用：
- `window.location.href = '/path'` - 適用於強制頁面重新載入
- 或者通過事件通知組件進行路由跳轉

### 2. Vue Composition API 限制
以下 API 只能在 Vue 組件的 setup 函數中使用：
- `useRouter()`
- `useRoute()`
- `useI18n()`
- 其他 `use*` 組合式 API

### 3. WebSocket 狀態管理
WebSocket 相關的狀態變數在模組級別定義，確保：
- 在整個應用中保持單一實例
- 正確的生命週期管理
- 避免內存洩漏

## 最佳實踐

### 1. Store 中的路由跳轉
```typescript
// ❌ 錯誤：在 store 中使用 useRouter
const router = useRouter();
router.push('/login');

// ✅ 正確：使用 window.location
window.location.href = '/login';

// ✅ 或者：通過事件通知組件
window.dispatchEvent(new CustomEvent('auth:logout'));
```

### 2. 跨分頁通信
```typescript
// ✅ 使用 localStorage 事件
localStorage.setItem('auth-logout-sync', 'true');
window.addEventListener('storage', (event) => {
  if (event.key === 'auth-logout-sync') {
    // 處理登出
  }
});
```

### 3. WebSocket 生命週期
```typescript
// ✅ 正確的清理
logout() {
  // 清理定時器
  if (wsReconnectTimer) {
    clearTimeout(wsReconnectTimer);
    wsReconnectTimer = null;
  }
  
  // 關閉連接
  if (ws) {
    ws.close(1000, 'User logout');
    ws = null;
  }
  
  // 重置狀態
  wsConnectionState = 'disconnected';
  wsReconnectAttempts = 0;
}
```

## 總結

所有的語法錯誤和邏輯問題都已修復：
- 移除重複的變數宣告
- 修復不正確的 `useRouter` 使用
- 移除未使用的導入
- 確保所有功能正常運作

文件現在應該能夠正常編譯和運行，沒有 TypeScript 錯誤或運行時問題。
