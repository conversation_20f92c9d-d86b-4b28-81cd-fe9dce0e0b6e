import { Notify } from 'quasar';
import { AxiosError } from 'axios';

export function handleError(error: unknown) {
  let message = '發生錯誤';

  if (error instanceof AxiosError) {
    message = error.response?.data.message || error.message;
  } else if (error instanceof Error) {
    message = error.message;
  } else if (typeof error === 'string') {
    message = error;
  }

  Notify.create({
    type: 'negative',
    message,
    position: 'top',
  });
}
