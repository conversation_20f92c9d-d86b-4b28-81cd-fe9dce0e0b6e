# UserDevice redeclared 錯誤修正

## 問題描述

在 `api/utils/device_cleanup.go` 文件中出現了 `UserDevice redeclared` 錯誤，這是因為：

1. **重複定義**：`UserDevice` 結構體已經在 `lottery/models` 包中定義
2. **循環導入**：嘗試導入 `lottery/models` 會造成循環導入問題
3. **命名衝突**：同一個項目中不能有相同名稱的結構體定義

## 解決方案

### 1. 重命名本地結構體

將 `device_cleanup.go` 中的 `UserDevice` 重命名為 `DeviceRecord`，避免與 `models` 包中的結構體衝突：

```go
// 修正前（會導致 redeclared 錯誤）
type UserDevice struct {
    ID             int64           `gorm:"primaryKey" json:"id"`
    UserID         uint64          `json:"user_id"`
    // ... 其他字段
}

// 修正後（使用不同的名稱）
type DeviceRecord struct {
    ID             int64           `gorm:"primaryKey" json:"id"`
    UserID         uint64          `json:"user_id"`
    // ... 其他字段
}
```

### 2. 更新 TableName 方法

確保新的結構體指向正確的數據庫表：

```go
// TableName 指定表名
func (DeviceRecord) TableName() string {
    return "user_devices"
}
```

### 3. 替換所有引用

將文件中所有的 `UserDevice` 引用替換為 `DeviceRecord`：

```go
// 修正前
var devices []UserDevice
err := s.db.Model(&UserDevice{}).Count(&totalDevices).Error

// 修正後
var devices []DeviceRecord
err := s.db.Model(&DeviceRecord{}).Count(&totalDevices).Error
```

## 修正的具體變更

### 1. 結構體定義
```go
// DeviceRecord 本地設備記錄結構體（避免循環導入）
type DeviceRecord struct {
    ID             int64           `gorm:"primaryKey" json:"id"`
    UserID         uint64          `json:"user_id"`
    DeviceID       string          `json:"device_id"`
    StableDeviceID string          `json:"stable_device_id"`
    DeviceName     string          `json:"device_name"`
    UserAgent      string          `json:"user_agent"`
    IsActive       bool            `json:"is_active"`
    LastSeenAt     time.Time       `json:"last_seen_at"`
    Confidence     int             `json:"confidence"`
    CreatedAt      time.Time       `json:"created_at"`
    UpdatedAt      time.Time       `json:"updated_at"`
    DeletedAt      *gorm.DeletedAt `gorm:"index" json:"-"`
}
```

### 2. 數據庫操作更新

**查詢操作**：
```go
// 生成缺失的穩定設備ID
var devices []DeviceRecord
err := s.db.Where("stable_device_id = '' OR stable_device_id IS NULL").Find(&devices).Error

// 統計查詢
err := s.db.Model(&DeviceRecord{}).Count(&totalDevices).Error
err = s.db.Model(&DeviceRecord{}).Where("is_active = true").Count(&activeDevices).Error
```

**聚合查詢**：
```go
// 查找重複設備組
err := s.db.Model(&DeviceRecord{}).
    Select("user_id, stable_device_id, COUNT(*) as count").
    Where("stable_device_id != '' AND stable_device_id IS NOT NULL").
    Group("user_id, stable_device_id").
    Having("COUNT(*) > 1").
    Find(&groups).Error
```

**刪除操作**：
```go
// 清理不活躍設備
result := s.db.Where("last_seen_at < ? AND is_active = false", cutoffTime).Delete(&DeviceRecord{})
```

### 3. 避免循環導入

**問題**：
```go
import (
    . "lottery/models"  // 這會導致循環導入
)
```

**解決方案**：
```go
import (
    "crypto/md5"
    "encoding/hex"
    "log"
    "regexp"
    "strings"
    "time"

    "gorm.io/gorm"
)
// 不導入 models 包，使用本地結構體定義
```

## 功能驗證

修正後的文件保持了所有原有功能：

### 1. 設備清理服務
```go
service := NewDeviceCleanupService(db)
err := service.CleanupDuplicateDevices()
```

### 2. 統計信息獲取
```go
stats, err := service.GetDeviceStatistics()
// 返回：
// - total_devices: 總設備數
// - active_devices: 活躍設備數
// - devices_with_stable_id: 有穩定設備ID的記錄數
// - duplicate_groups: 重複設備組數
// - average_confidence: 平均可信度
```

### 3. 設備記錄操作
- 生成缺失的穩定設備ID
- 合併同一物理設備的記錄
- 清理長期不活躍的設備記錄

## 編譯驗證

修正後應該能夠正常編譯：

```bash
cd api
go build ./utils/device_cleanup.go
```

不應該再出現以下錯誤：
- ❌ `UserDevice redeclared`
- ❌ `import cycle not allowed`

## 使用示例

```go
package main

import (
    "log"
    "lottery/database"
    "lottery/utils"
)

func main() {
    // 連接數據庫
    db := database.ConnectDB()
    defer database.CloseDB(db)

    // 創建清理服務
    cleanupService := utils.NewDeviceCleanupService(db)

    // 執行清理
    err := cleanupService.CleanupDuplicateDevices()
    if err != nil {
        log.Fatal("清理失敗:", err)
    }

    // 獲取統計信息
    stats, err := cleanupService.GetDeviceStatistics()
    if err != nil {
        log.Fatal("獲取統計失敗:", err)
    }
    
    log.Printf("設備統計: %+v", stats)
}
```

## 總結

通過以下修正解決了 `UserDevice redeclared` 錯誤：

1. ✅ **重命名結構體**：`UserDevice` → `DeviceRecord`
2. ✅ **避免循環導入**：不導入 `lottery/models` 包
3. ✅ **保持功能完整**：所有原有功能都正常工作
4. ✅ **數據庫兼容**：通過 `TableName()` 方法指向正確的表
5. ✅ **編譯正常**：不再有重複聲明錯誤

這個解決方案既避免了命名衝突，又保持了代碼的功能完整性和可維護性。
