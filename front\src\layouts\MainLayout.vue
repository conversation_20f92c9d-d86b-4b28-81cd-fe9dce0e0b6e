<template>
  <q-layout view="hHh Lpr lFf">
    <q-header elevated>
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title>{{ title.val }}</q-toolbar-title>

        <q-space />

        <div class="q-mr-md">
          {{ authStore.userInfo?.name }}
        </div>
        <q-btn
          flat
          dense
          round
          icon="logout"
          aria-label="Logout"
          @click="logout"
        />
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" bordered class="drawer-container">
      <div class="drawer-content">
        <q-list class="menu-list">
          <MenuLink
            v-for="link in linksList"
            :key="link.title"
            v-bind="link"
            @toggle-menu="toggleLeftDrawer"
          />
        </q-list>
      </div>

      <!-- 版本號 -->
      <div class="version-footer">
        <q-separator />
        <div class="text-caption text-center text-grey-6 q-pa-md">
          版本:{{ appVersion }}
        </div>
      </div>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import MenuLink, { MenuLinkProps } from '@/components/Menu.vue';
import { useTitleStore } from '@/stores/title';
import { useAuthStore } from '@/stores/auth';
import AUTH_API from '@/api/modules/auth';
import packageJson from '@/../package.json';

defineOptions({
  name: 'MainLayout',
});

const appVersion = ref(packageJson.version);

const router = useRouter();
const title = useTitleStore();
const authStore = useAuthStore();

const linksList: MenuLinkProps[] = [
  {
    title: '會員管理',
    link: '/admin/dashboard/user',
    requireAdmin: true,
  },
  {
    title: '個人資料',
    link: '/profile',
  },
  {
    title: '開獎結果',
    link: '/lotto-results',
  },
  {
    title: '版路分析',
    link: '/rd1',
  },
  {
    title: '尾數分析',
    link: '/tail',
  },
  {
    title: '綜合分析',
    link: '/pattern',
  },
  {
    title: '分析報表',
    link: '/batch-analysis',
  },
  {
    title: '安裝程式',
    link: '/install',
  },
  {
    title: '免責聲明',
    link: '/disclaimer',
  },
  {
    title: '使用者服務條款',
    link: '/user-agreement',
  },
];

const leftDrawerOpen = ref(false);

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;
}

const logout = async () => {
  await AUTH_API.logout();
  authStore.logout();
  router.push('/login');
};
</script>

<style lang="scss">
* {
  -webkit-user-select: none;
  user-select: none;
}

.q-page-container {
  width: 80%;
  margin: 1rem auto;
  padding-right: 16px;
  padding-left: 16px;
}

.drawer-container {
  display: flex;
  flex-direction: column;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;

  .q-item__label {
    font-size: 1.4rem;
  }

  .menu-list {
    .q-item {
      margin-bottom: 2px;
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      background-color: rgba(250, 250, 250, 0.5);

      &:hover {
        background-color: rgba(25, 118, 210, 0.06);
      }

      &.q-router-link--active {
        background-color: rgba(25, 118, 210, 0.12);
        border-color: #1976d2;
        border-width: 2px;

        .q-item__label {
          color: #1976d2;
          font-weight: 500;
        }
      }

      .q-item__section {
        padding: 10px 14px;
      }

      .q-item__label {
        line-height: 1.4;
        padding: 2px 0;
      }
    }

    // 每個項目之間添加分隔線
    .q-item + .q-item {
      border-top: 2px solid rgba(0, 0, 0, 0.2);
      margin-top: 2px;
    }

    // 為每個按鈕添加不同的左側顏色標記
    .q-item:nth-child(odd) {
      border-left: 3px solid rgba(25, 118, 210, 0.6);
    }

    .q-item:nth-child(even) {
      border-left: 3px solid rgba(96, 125, 139, 0.6);
    }
  }
}

.version-footer {
  margin-top: auto;
}

@media (max-width: 991px) {
  .q-page-container {
    width: 100%;
  }

  .drawer-content {
    .q-item__label {
      font-size: 1.7rem;
    }

    .menu-list {
      .q-item {
        margin-bottom: 3px;

        .q-item__section {
          padding: 12px 16px;
        }

        .q-item__label {
          padding: 4px 0;
        }
      }
    }
  }
}
</style>
