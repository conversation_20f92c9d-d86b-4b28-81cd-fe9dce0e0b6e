<template>
  <q-page class="q-pa-md">
    <div class="row q-gutter-md">
      <div class="col-12">
        <h4>跨分頁同步測試</h4>
        <p class="text-grey-7">
          此頁面用於測試 token 跨分頁同步功能。請在多個分頁中打開此頁面進行測試。
        </p>
      </div>

      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">當前 Token 狀態</div>
          </q-card-section>
          <q-card-section>
            <div class="q-mb-sm">
              <strong>Access Token:</strong>
              <div class="text-caption text-grey-7 q-mt-xs">
                {{ authStore.accessToken || '無' }}
              </div>
            </div>
            <div class="q-mb-sm">
              <strong>Token 過期時間:</strong>
              <div class="text-caption text-grey-7 q-mt-xs">
                {{ authStore.tokenExpiry ? formatDate(authStore.tokenExpiry) : '無' }}
              </div>
            </div>
            <div class="q-mb-sm">
              <strong>Refresh <PERSON>ken:</strong>
              <div class="text-caption text-grey-7 q-mt-xs">
                {{ authStore.refreshToken || '無' }}
              </div>
            </div>
            <div class="q-mb-sm">
              <strong>是否已認證:</strong>
              <q-chip :color="authStore.isAuthenticated ? 'green' : 'red'" text-color="white">
                {{ authStore.isAuthenticated ? '是' : '否' }}
              </q-chip>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">測試操作</div>
          </q-card-section>
          <q-card-section class="q-gutter-sm">
            <q-btn
              color="primary"
              @click="testTokenUpdate"
              :loading="isTestingToken"
            >
              測試 Token 更新
            </q-btn>
            <q-btn
              color="orange"
              @click="testLogout"
              :loading="isTestingLogout"
            >
              測試登出同步
            </q-btn>
            <q-btn
              color="secondary"
              @click="simulateTokenRefresh"
              :loading="isRefreshing"
            >
              模擬 Token 刷新
            </q-btn>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">同步事件日誌</div>
            <q-btn
              flat
              dense
              icon="clear"
              @click="clearLogs"
              class="float-right"
            >
              清除日誌
            </q-btn>
          </q-card-section>
          <q-card-section>
            <div class="sync-logs">
              <div
                v-for="(log, index) in syncLogs"
                :key="index"
                class="log-entry q-mb-xs"
                :class="log.type"
              >
                <span class="timestamp">{{ log.timestamp }}</span>
                <span class="message">{{ log.message }}</span>
              </div>
              <div v-if="syncLogs.length === 0" class="text-grey-5">
                暫無同步事件
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { crossTabSyncTester } from '@/utils/crossTabSync';

defineOptions({
  name: 'CrossTabTestPage',
});

const authStore = useAuthStore();

const isTestingToken = ref(false);
const isTestingLogout = ref(false);
const isRefreshing = ref(false);

interface SyncLog {
  timestamp: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

const syncLogs = ref<SyncLog[]>([]);

const addLog = (message: string, type: SyncLog['type'] = 'info') => {
  syncLogs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  });

  // 限制日誌數量
  if (syncLogs.value.length > 50) {
    syncLogs.value = syncLogs.value.slice(0, 50);
  }
};

const formatDate = (date: Date) => {
  return new Date(date).toLocaleString();
};

const testTokenUpdate = async () => {
  isTestingToken.value = true;
  addLog('開始測試 Token 更新同步', 'info');

  try {
    crossTabSyncTester.testTokenSync();
    addLog('Token 更新測試完成', 'success');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    addLog(`Token 更新測試失敗: ${errorMessage}`, 'error');
  } finally {
    isTestingToken.value = false;
  }
};

const testLogout = async () => {
  isTestingLogout.value = true;
  addLog('開始測試登出同步', 'info');

  try {
    crossTabSyncTester.testLogoutSync();
    addLog('登出同步測試完成', 'success');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    addLog(`登出同步測試失敗: ${errorMessage}`, 'error');
  } finally {
    isTestingLogout.value = false;
  }
};

const simulateTokenRefresh = async () => {
  isRefreshing.value = true;
  addLog('開始模擬 Token 刷新', 'info');

  try {
    // 模擬新的 token 數據
    const mockTokenData = {
      access_token: `mock-access-${Date.now()}`,
      expires_at: new Date(Date.now() + 3600000),
      refresh_token: `mock-refresh-${Date.now()}`,
      refresh_expires_at: new Date(Date.now() + 7200000)
    };

    authStore.updateToken(mockTokenData);
    addLog('Token 刷新模擬完成', 'success');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    addLog(`Token 刷新模擬失敗: ${errorMessage}`, 'error');
  } finally {
    isRefreshing.value = false;
  }
};

const clearLogs = () => {
  syncLogs.value = [];
  addLog('日誌已清除', 'info');
};

// 設置存儲事件監聽器
const setupStorageListeners = () => {
  const tokenSyncListener = (event: StorageEvent) => {
    if (event.key === 'auth-token-sync') {
      addLog('檢測到 Token 同步事件', 'success');
    }
  };

  const logoutSyncListener = (event: StorageEvent) => {
    if (event.key === 'auth-logout-sync') {
      addLog('檢測到登出同步事件', 'warning');
    }
  };

  crossTabSyncTester.addListener('auth-token-sync', tokenSyncListener);
  crossTabSyncTester.addListener('auth-logout-sync', logoutSyncListener);

  return () => {
    crossTabSyncTester.removeListener('auth-token-sync', tokenSyncListener);
    crossTabSyncTester.removeListener('auth-logout-sync', logoutSyncListener);
  };
};

let cleanupListeners: (() => void) | null = null;

onMounted(() => {
  addLog('跨分頁測試頁面已載入', 'info');
  cleanupListeners = setupStorageListeners();
});

onUnmounted(() => {
  if (cleanupListeners) {
    cleanupListeners();
  }
});
</script>

<style scoped>
.sync-logs {
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-entry {
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 2px;
}

.log-entry.info {
  background-color: #e3f2fd;
  color: #1976d2;
}

.log-entry.success {
  background-color: #e8f5e9;
  color: #388e3c;
}

.log-entry.warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.log-entry.error {
  background-color: #ffebee;
  color: #d32f2f;
}

.timestamp {
  font-weight: bold;
  margin-right: 8px;
}

.message {
  word-break: break-word;
}
</style>
