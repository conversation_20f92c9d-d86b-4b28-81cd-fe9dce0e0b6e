# Token刷新邏輯修復文檔

## 問題描述

在 `front/src/stores/auth.ts` 的 `validateToken` 方法中存在一個重要問題：當token過期時，會直接讓使用者登出，並沒有嘗試使用refreshToken進行更新Token。

## 問題分析

### 修復前的問題代碼
```typescript
async validateToken(): Promise<boolean> {
  // ... 驗證邏輯 ...
  
  if (response.status === 401) {
    // ❌ 問題：直接登出，沒有嘗試刷新token
    console.log('Token已失效，清除本地數據');
    this.logout();
    return false;
  }
}
```

### 問題影響
1. **用戶體驗差**：token過期時用戶被強制登出，需要重新輸入帳密
2. **功能不完整**：沒有充分利用refresh token的自動刷新機制
3. **邏輯不一致**：`checkAuthentication` 方法有刷新邏輯，但 `validateToken` 沒有

## 修復方案

### 1. 修復 `validateToken` 方法
```typescript
async validateToken(): Promise<boolean> {
  // ... 驗證邏輯 ...
  
  if (response.status === 401) {
    // ✅ 修復：嘗試使用refresh token刷新
    console.log('Token已失效，嘗試刷新token');
    
    if (!this.isRefreshTokenExpired) {
      try {
        console.log('🔄 使用refresh token刷新access token');
        const AUTH_API = (await import('@/api/modules/auth')).default;
        const response = await AUTH_API.refreshToken();
        this.updateToken(response.data);
        console.log('✅ Token刷新成功');
        return true;
      } catch (error) {
        console.error('❌ 刷新token失敗:', error);
        console.log('Refresh token也已失效，執行登出');
        this.logout();
        return false;
      }
    } else {
      console.log('Refresh token已過期，執行登出');
      this.logout();
      return false;
    }
  }
}
```

### 2. 簡化 `checkAuthentication` 方法
由於 `validateToken` 現在已經包含了刷新邏輯，`checkAuthentication` 可以簡化：

```typescript
async checkAuthentication(): Promise<boolean> {
  // 首先檢查本地是否有token
  if (!this.hasLocalToken) {
    return false;
  }

  // 如果本地token已過期且refresh token也過期，直接登出
  if (this.isTokenExpired && this.isRefreshTokenExpired) {
    console.log('Access token和refresh token都已過期，執行登出');
    this.logout();
    return false;
  }

  // 驗證token在後端是否仍然有效（包含自動刷新邏輯）
  return await this.validateToken();
}
```

## 修復效果

### 1. 自動Token刷新
- ✅ 當access token過期時，自動嘗試使用refresh token刷新
- ✅ 刷新成功後用戶可以繼續使用，無需重新登入
- ✅ 只有當refresh token也過期時才會登出用戶

### 2. 改進的用戶體驗
- ✅ 減少不必要的登出
- ✅ 無縫的token更新體驗
- ✅ 更好的會話持續性

### 3. 邏輯一致性
- ✅ `validateToken` 和 `checkAuthentication` 都有完整的刷新邏輯
- ✅ 避免重複代碼
- ✅ 統一的錯誤處理

## 使用場景

### 1. 定期Token驗證
```typescript
// 每5分鐘驗證一次token
setInterval(async () => {
  if (this.hasLocalToken && !this.isTokenExpired) {
    const isValid = await this.validateToken();
    if (!isValid) {
      console.log('定期驗證發現token已失效');
      // 如果刷新失敗，會自動登出
    }
  }
}, 5 * 60 * 1000);
```

### 2. 路由守衛
```typescript
// 在路由守衛中使用
const isAuthenticated = await authStore.checkAuthentication();
if (!isAuthenticated) {
  // 重定向到登入頁面
  return '/login';
}
```

### 3. API請求前驗證
```typescript
// 在重要API請求前驗證token
const isValid = await authStore.validateToken();
if (isValid) {
  // 執行API請求
  const response = await api.get('/important-data');
}
```

## 日誌輸出

修復後的日誌輸出更加詳細：

### 成功刷新的情況
```
Token已失效，嘗試刷新token
🔄 使用refresh token刷新access token
✅ Token刷新成功
```

### 刷新失敗的情況
```
Token已失效，嘗試刷新token
🔄 使用refresh token刷新access token
❌ 刷新token失敗: [錯誤詳情]
Refresh token也已失效，執行登出
```

### Refresh Token過期的情況
```
Token已失效，嘗試刷新token
Refresh token已過期，執行登出
```

## 測試建議

### 1. Token過期測試
1. 登入系統
2. 手動修改localStorage中的token過期時間
3. 觸發需要驗證的操作
4. 驗證是否自動刷新token

### 2. Refresh Token過期測試
1. 登入系統
2. 手動修改localStorage中的refresh token過期時間
3. 觸發需要驗證的操作
4. 驗證是否正確登出

### 3. 網絡錯誤測試
1. 登入系統
2. 斷開網絡連接
3. 觸發token驗證
4. 驗證錯誤處理是否正確

## 注意事項

1. **安全性**：refresh token的處理仍然遵循安全最佳實踐
2. **性能**：避免不必要的刷新請求
3. **用戶體驗**：在刷新過程中保持UI響應
4. **錯誤處理**：妥善處理各種錯誤情況

## 總結

這個修復解決了token管理中的一個重要缺陷，提供了：

- ✅ **完整的token生命週期管理**
- ✅ **自動刷新機制**
- ✅ **更好的用戶體驗**
- ✅ **一致的邏輯處理**
- ✅ **詳細的日誌記錄**

現在用戶在token過期時不會被立即登出，系統會自動嘗試刷新token，只有在refresh token也過期時才會要求用戶重新登入。
