// 版本檢查服務
export class VersionChecker {
  private static instance: VersionChecker;
  private currentVersion: string;
  private checkInterval: number | null = null;
  private buildVersion: string;
  private useSmartUpdate = true; // 是否使用智能更新（由App.vue處理）

  private constructor() {
    // 從環境變數獲取構建版本（包含時間戳）
    this.buildVersion = process.env.APP_VERSION || 'unknown';
    // 提取基礎版本號（去掉時間戳）
    this.currentVersion = this.extractBaseVersion(this.buildVersion);
  }

  private extractBaseVersion(fullVersion: string): string {
    // 從 "v1.2.4_123456" 格式中提取 "v1.2.4"
    const match = fullVersion.match(/^(v\d+\.\d+\.\d+)/);
    return match ? match[1] : fullVersion;
  }

  public static getInstance(): VersionChecker {
    if (!VersionChecker.instance) {
      VersionChecker.instance = new VersionChecker();
    }
    return VersionChecker.instance;
  }

  public startVersionCheck() {
    // 在開發環境中完全禁用版本檢查
    if (process.env.DEV) {
      return;
    }

    // 生產環境每60秒檢查一次版本
    this.checkInterval = window.setInterval(() => {
      this.checkVersion();
    }, 60000);

    // 延遲10秒後開始第一次檢查
    setTimeout(() => this.checkVersion(), 10000);
  }

  private async checkVersion() {
    // 在開發環境中不執行版本檢查
    if (process.env.DEV) {
      return;
    }

    try {

      // 方法1: 檢查 index.html 的 ETag/Last-Modified
      const indexResponse = await fetch('/index.html?' + Date.now(), {
        method: 'HEAD',
        cache: 'no-cache'
      });

      if (indexResponse.ok) {
        const etag = indexResponse.headers.get('etag');
        const lastModified = indexResponse.headers.get('last-modified');

        const storedEtag = localStorage.getItem('app-etag');
        const storedLastModified = localStorage.getItem('app-last-modified');

        // 檢查是否為第一次訪問（沒有儲存的值）
        const isFirstVisit = !storedEtag && !storedLastModified;

        if (!isFirstVisit) {
          // 不是第一次訪問，檢查是否有變更
          if (etag && storedEtag && etag !== storedEtag) {
            // VersionChecker: 檢測到新版本 (ETag 變更)
            this.handleNewVersion();
            return;
          }

          if (lastModified && storedLastModified && lastModified !== storedLastModified) {
            // VersionChecker: 檢測到新版本 (Last-Modified 變更)
            this.handleNewVersion();
            return;
          }
        }

        // 儲存當前的標頭值（第一次訪問或沒有變更時）
        if (etag) localStorage.setItem('app-etag', etag);
        if (lastModified) localStorage.setItem('app-last-modified', lastModified);
      }

      // 方法2: 檢查版本文件（如果存在）
      await this.checkVersionFile();

    } catch (error) {
      console.error('VersionChecker: 版本檢查失敗', error);
    }
  }

  private async checkVersionFile() {
    // 在開發環境中不執行版本文件檢查
    if (process.env.DEV) {
      return;
    }

    try {
      // 嘗試獲取版本信息文件
      const response = await fetch('/version.json?' + Date.now(), {
        cache: 'no-cache'
      });

      if (response.ok) {
        const versionData = await response.json();
        const serverVersion = versionData.version;

        // 檢查是否為第一次訪問（沒有儲存的版本信息）
        const storedVersion = localStorage.getItem('app-stored-version');
        const isFirstVisit = !storedVersion;

        if (!isFirstVisit && serverVersion && serverVersion !== this.buildVersion) {
          // 不是第一次訪問且檢測到新版本
          this.handleNewVersion();
        }

        // 儲存當前版本信息（第一次訪問或沒有變更時）
        if (serverVersion) {
          localStorage.setItem('app-stored-version', serverVersion);
        }
      }
    } catch (error) {
      // 版本文件不存在或無法讀取，這是正常的
      console.log('VersionChecker: 版本文件檢查失敗（可能不存在）', error instanceof Error ? error.message : String(error));
    }
  }

  // 發現新版本，準備更新
  private handleNewVersion() {
    // 發送自定義事件通知App.vue，讓App.vue統一處理更新邏輯
    const event = new CustomEvent('version-update-detected', {
      detail: {
        currentVersion: this.buildVersion,
        isDev: process.env.DEV
      }
    });
    window.dispatchEvent(event);

    // 在開發環境中直接刷新
    if (process.env.DEV) {
      this.performDirectUpdate();
    } else if (this.useSmartUpdate) {
      // 智能更新模式：由App.vue統一處理更新邏輯
    } else {
      // 傳統模式：顯示versionChecker自己的對話框
      this.showUpdateDialog();
    }
  }

  private showUpdateDialog() {
    // 創建更新提示對話框
    const dialog = document.createElement('div');
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `;

    dialogContent.innerHTML = `
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          🎉 發現新版本！
        </div>
        <div style="color: #666; font-size: 14px;">
          應用程式有新版本可用，是否要立即更新？
        </div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="update-later" style="
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">稍後更新</button>
        <button id="update-now" style="
          padding: 8px 16px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">立即更新</button>
      </div>
    `;

    dialog.appendChild(dialogContent);
    document.body.appendChild(dialog);

    // 綁定按鈕事件
    const updateNowBtn = dialog.querySelector('#update-now');
    const updateLaterBtn = dialog.querySelector('#update-later');

    updateNowBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      this.performUpdate();
    });

    updateLaterBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      console.log('VersionChecker: 用戶選擇稍後更新');
      // 設置一個標記，30分鐘後再次提醒
      setTimeout(() => {
        this.showUpdateDialog();
      }, 30 * 60 * 1000); // 30分鐘
    });
  }

  private async performUpdate() {
    console.log('VersionChecker: 開始執行更新');

    // 顯示更新進度
    this.showUpdateProgress();

    try {
      // 清除所有緩存
      await this.clearAllCaches();

      // 延遲一下讓用戶看到進度
      setTimeout(() => {
        // 重新載入頁面
        window.location.reload();
      }, 1500);
    } catch (error) {
      console.error('VersionChecker: 更新失敗', error);
      this.showUpdateError();
    }
  }

  private async performDirectUpdate() {
    console.log('VersionChecker: 開發環境直接更新');

    try {
      // 清除所有緩存
      await this.clearAllCaches();

      // 直接重新載入頁面，不顯示進度
      window.location.reload();
    } catch (error) {
      console.error('VersionChecker: 直接更新失敗', error);
      // 即使清除緩存失敗，也嘗試刷新頁面
      window.location.reload();
    }
  }

  private showUpdateProgress() {
    const progressDialog = document.createElement('div');
    progressDialog.id = 'update-progress-dialog';
    progressDialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10001;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const progressContent = document.createElement('div');
    progressContent.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      text-align: center;
    `;

    progressContent.innerHTML = `
      <div style="margin-bottom: 16px;">
        <div style="font-size: 16px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          正在更新...
        </div>
        <div style="color: #666; font-size: 14px;">
          請稍候，正在下載最新版本
        </div>
      </div>
      <div style="width: 40px; height: 40px; margin: 0 auto;">
        <div style="
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #1976d2;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;

    progressDialog.appendChild(progressContent);
    document.body.appendChild(progressDialog);
  }

  private showUpdateError() {
    // 移除進度對話框
    const progressDialog = document.getElementById('update-progress-dialog');
    if (progressDialog) {
      document.body.removeChild(progressDialog);
    }

    const errorDialog = document.createElement('div');
    errorDialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10002;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const errorContent = document.createElement('div');
    errorContent.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `;

    errorContent.innerHTML = `
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #d32f2f; margin-bottom: 8px;">
          ❌ 更新失敗
        </div>
        <div style="color: #666; font-size: 14px;">
          更新過程中發生錯誤，請手動重新整理頁面
        </div>
      </div>
      <button id="manual-reload" style="
        padding: 8px 16px;
        border: none;
        background: #1976d2;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      ">手動重新整理</button>
    `;

    errorDialog.appendChild(errorContent);
    document.body.appendChild(errorDialog);

    // 綁定手動重新整理按鈕
    const manualReloadBtn = errorDialog.querySelector('#manual-reload');
    manualReloadBtn?.addEventListener('click', () => {
      window.location.reload();
    });
  }

  private async clearAllCaches() {
    try {
      // 清除 Service Worker 緩存
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('VersionChecker: 已清除所有緩存');
      }

      // 清除 localStorage 中的版本相關資訊
      localStorage.removeItem('app-etag');
      localStorage.removeItem('app-last-modified');
      localStorage.removeItem('app-stored-version');
    } catch (error) {
      console.error('VersionChecker: 清除緩存失敗', error);
    }
  }

  public destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  public getCurrentVersion(): string {
    return this.currentVersion;
  }

  public getBuildVersion(): string {
    return this.buildVersion;
  }

  public getVersionInfo() {
    return {
      baseVersion: this.currentVersion,
      buildVersion: this.buildVersion,
      timestamp: this.extractTimestamp(this.buildVersion)
    };
  }

  private extractTimestamp(fullVersion: string): string {
    // 從 "v1.2.4_123456" 格式中提取時間戳
    const match = fullVersion.match(/_(\d+)$/);
    return match ? match[1] : '';
  }

  // 公共更新方法
  public async triggerUpdate(): Promise<void> {
    return this.performUpdate();
  }

  // 設置是否使用智能更新
  public setUseSmartUpdate(useSmartUpdate: boolean): void {
    this.useSmartUpdate = useSmartUpdate;
  }

  // 手動檢查版本
  public async manualVersionCheck(): Promise<boolean> {
    // 在開發環境中不執行手動版本檢查
    if (process.env.DEV) {
      return false;
    }

    try {
      // 檢查 index.html 的變更
      const indexChanged = await this.checkIndexChanges();
      if (indexChanged) {
        return true;
      }

      // 檢查版本文件
      const versionChanged = await this.checkVersionFileChanges();
      if (versionChanged) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('VersionChecker: 手動版本檢查失敗', error);
      throw error;
    }
  }

  private async checkIndexChanges(): Promise<boolean> {
    const response = await fetch('/index.html?' + Date.now(), {
      method: 'HEAD',
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error('無法獲取 index.html');
    }

    const etag = response.headers.get('etag');
    const lastModified = response.headers.get('last-modified');

    const storedEtag = localStorage.getItem('app-etag');
    const storedLastModified = localStorage.getItem('app-last-modified');

    if (etag && storedEtag && etag !== storedEtag) {
      return true;
    }

    if (lastModified && storedLastModified && lastModified !== storedLastModified) {
      return true;
    }

    return false;
  }

  private async checkVersionFileChanges(): Promise<boolean> {
    // 在開發環境中跳過版本文件檢查
    if (process.env.DEV) {
      return false;
    }

    try {
      const response = await fetch('/version.json?' + Date.now(), {
        cache: 'no-cache'
      });

      if (response.ok) {
        const versionData = await response.json();
        const serverVersion = versionData.version;

        if (serverVersion && serverVersion !== this.buildVersion) {
          // 服務器版本與本地版本不同
          return true;
        }
      }
      return false;
    } catch (error) {
      // 版本文件不存在是正常的
      console.log('VersionChecker: 版本文件不存在或無法訪問');
      return false;
    }
  }
}

// 創建全局實例
export const versionChecker = VersionChecker.getInstance();
