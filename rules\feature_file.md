# Project Environment

1. 前端: Vue3 + Quasar UI PWA
2. 後端: Golang + Gin + Gorm
3. 資料庫: MySQL
4. Migration: Golang-Migrate，於路徑api/database/migrations，依照當前檔案順序，生成MySQL migration up/down檔案
5. 使用者使用環境包含電腦、平板、手機，所有前端介面皆需考量以上裝置，讓使用者可以在不同裝置上使用

# 開發規則

1. 前端使用 TypeScript，不能使用 any, unknown 類型，每個變數皆需指定類型
2. 前端使用盡可能使用 Quasar UI 的元素
3. 後端 Golang 嚴格遵守 Clean Code 規範
4. 嚴格遵守 DRY (Don't Repeat Yourself) 原則，如有違反，則進行統合、優化修改
5. 不需要多餘的 console、log 等文字顯示，請以註解取代，說明當前程式碼的作用