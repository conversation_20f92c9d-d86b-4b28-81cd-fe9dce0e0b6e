import { Dialog } from 'quasar';

interface ShowDialogOptions {
  message: string;
  title?: string;
  timeout?: number;
  persistent?: boolean;
  color?: string;
  ok?: () => void;
  onRedirect?: () => void;
}

export const useDialog = () => {
  const showMessage = ({
    message,
    title = '提示',
    timeout = 1500,
    persistent = false,
    color = 'primary',
    ok,
    onRedirect,
  }: ShowDialogOptions) => {
    const dialog = Dialog.create({
      title,
      message,
      color,
      persistent,
      html: true,
      style: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
      },
      ok: {
        label: '確定',
        color: 'primary',
      },
      cancel:
        timeout > 0
          ? undefined
          : {
              label: '取消',
              color: 'negative',
            },
    }).onOk(() => {
      if (ok) {
        ok();
      } else if (onRedirect) {
        onRedirect();
      }
    });

    if (timeout > 0) {
      setTimeout(() => {
        dialog.hide();
        if (onRedirect) {
          onRedirect();
        }
      }, timeout);
    }
  };

  return {
    showMessage,
  };
};
