package main

import (
	"os"
	"time"
	_ "time/tzdata"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	"lottery/routes"
	. "lottery/utils"
)

func init() {
	log.SetFormatter(&log.JSONFormatter{})
	log.SetOutput(os.Stdout)

	// set time zone
	twTimeZone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		ErrorLog(ErrorMsg{
			Msg:   "無法設定時區",
			Error: err.<PERSON>rror(),
		})
	}

	time.Local = twTimeZone
}

func main() {
	router := routes.Routes()

	go func() {
		if err := router.Run(":" + viper.GetString("APP_PORT")); err != nil {
			ErrorLog(ErrorMsg{
				Msg:   "無法啟動伺服器",
				Error: err.<PERSON><PERSON><PERSON>(),
			})
		}
	}()

	select {}
}
