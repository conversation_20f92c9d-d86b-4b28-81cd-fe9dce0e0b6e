# WebSocket 連接優化文檔

## 問題分析

### 1. 開發環境連接問題
- **問題**：開發環境前端運行在9000端口，WebSocket直接連接到8088端口，繞過了開發服務器的代理
- **影響**：連接不穩定，CORS問題，無法利用開發服務器的熱重載等功能

### 2. 連接失敗後的處理問題
- **問題**：如果初始WebSocket連接失敗，沒有重試機制
- **影響**：用戶需要手動刷新頁面才能重新建立連接

### 3. 強制登出通知遺漏
- **問題**：如果設備A未連接WebSocket，設備B登入時無法通知設備A登出
- **影響**：設備A在下次操作時才會發現token失效，存在安全風險

## 解決方案

### 1. 環境區分的WebSocket URL配置

```typescript
// 開發環境：使用開發服務器的代理
if (import.meta.env.DEV) {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  wsUrl = `${protocol}//${window.location.host}/api/ws`;
} else {
  // 生產環境：直接連接到後端服務器
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.hostname;
  const port = '8088';
  wsUrl = `${protocol}//${host}:${port}/api/ws`;
}
```

### 2. 智能重連機制

#### 連接狀態管理
- `disconnected`: 未連接
- `connecting`: 連接中
- `connected`: 已連接
- `failed`: 連接失敗

#### 重連策略
- **指數退避**：重連延遲逐漸增加（3秒 → 6秒 → 12秒 → 24秒 → 30秒）
- **最大重試次數**：5次
- **連接超時**：10秒
- **自動重置**：連接成功後重置重連次數

#### 重連觸發條件
- WebSocket連接錯誤
- 連接異常關閉（非1000狀態碼）
- 連接超時
- 頁面重新可見時檢查連接狀態

### 3. 強制登出通知改進

#### 後端改進
- **連接時檢查**：WebSocket連接建立時檢查是否有待處理的登出通知
- **活躍設備檢測**：檢查用戶是否有多個活躍設備
- **即時通知**：發現異常情況立即發送強制登出通知

#### 前端改進
- **強制重連**：登入成功後強制重新建立WebSocket連接
- **狀態監控**：提供WebSocket連接狀態查詢接口
- **手動重連**：提供強制重連功能

## 技術實現

### 前端改進

#### 1. 連接狀態管理
```typescript
let wsConnectionState: 'disconnected' | 'connecting' | 'connected' | 'failed' = 'disconnected';
let wsReconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000;
```

#### 2. 重連調度
```typescript
scheduleWebSocketReconnect() {
  const delay = Math.min(RECONNECT_INTERVAL * Math.pow(2, wsReconnectAttempts), 30000);
  wsReconnectTimer = setTimeout(() => {
    if (this.hasLocalToken && !this.isTokenExpired) {
      this.setupWebSocket(true);
    }
  }, delay);
}
```

#### 3. 強制重連
```typescript
forceReconnectWebSocket() {
  wsReconnectAttempts = 0;
  wsConnectionState = 'disconnected';
  this.setupWebSocket();
}
```

### 後端改進

#### 1. 連接時檢查
```go
// 檢查待處理的強制登出通知
go checkPendingLogoutNotification(userID, deviceID, conn)
```

#### 2. 活躍設備檢測
```go
func checkPendingLogoutNotification(userID, deviceID string, conn *websocket.Conn) {
  // 檢查是否有其他活躍的設備
  var activeDeviceCount int64
  db.Model(&UserDevice{}).Where("user_id = ? AND is_active = ?", userIDInt, true).Count(&activeDeviceCount)
  
  if activeDeviceCount > 1 {
    // 發送強制登出通知
  }
}
```

## 測試功能

### WebSocket測試頁面增強
- **連接狀態顯示**：實時顯示Auth Store的WebSocket狀態
- **重連次數監控**：顯示當前重連次數和最大重試次數
- **強制重連按鈕**：手動觸發WebSocket重連
- **後端連接測試**：測試HTTP API和WebSocket服務器可達性

### 測試場景

#### 1. 開發環境測試
```bash
# 啟動後端
cd api && go run main.go

# 啟動前端開發服務器
cd front && npm run dev

# 訪問 http://localhost:9000/websocket-test
```

#### 2. 強制登出測試
1. 設備A登入並建立WebSocket連接
2. 設備B用同一帳號登入
3. 驗證設備A是否收到強制登出通知

#### 3. 重連測試
1. 登入後斷開網絡連接
2. 恢復網絡連接
3. 驗證WebSocket是否自動重連

#### 4. 連接失敗測試
1. 關閉後端服務器
2. 嘗試登入
3. 啟動後端服務器
4. 驗證WebSocket是否自動連接

## 性能優化

### 1. 連接池管理
- 避免重複連接
- 正確清理資源
- 連接狀態同步

### 2. 重連策略優化
- 指數退避避免服務器壓力
- 最大重試次數防止無限重連
- 連接成功後重置計數器

### 3. 內存管理
- 及時清理定時器
- 正確關閉WebSocket連接
- 避免內存洩漏

## 配置說明

### 環境變數
```bash
# 自定義WebSocket URL（可選）
VITE_WS_URL=ws://custom-host:port/api/ws
```

### 開發環境
- WebSocket通過開發服務器代理：`ws://localhost:9000/api/ws`
- 自動利用Vite的熱重載和代理功能

### 生產環境
- WebSocket直接連接後端：`ws://domain:8088/api/ws`
- 支持HTTPS環境自動切換到WSS

## 監控和調試

### 日誌輸出
- 連接狀態變化
- 重連嘗試記錄
- 錯誤詳細信息
- 強制登出通知

### 狀態查詢
```typescript
const wsState = authStore.getWebSocketState();
// {
//   state: 'connected',
//   reconnectAttempts: 0,
//   maxAttempts: 5,
//   isConnected: true
// }
```

## 未來改進

1. **智能重連**：基於網絡狀態和用戶活動調整重連策略
2. **連接質量監控**：監控連接延遲和穩定性
3. **離線支持**：離線狀態下的消息緩存和同步
4. **多實例支持**：支持後端多實例部署的負載均衡
