<template>
  <q-layout view="hHh Lpr lFr">
    <q-header reveal elevated>
      <q-toolbar>
        <q-toolbar-title class="text-center">
          {{ title.val }}
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container>
      <router-view />
    </q-page-container>

    <q-footer bordered class="footer user-select-all">
      <div class="footer-content">
        <div class="contact-info">
          <span class="text-subtitle1">客服專線: 0979-139-262</span>
          <span class="text-subtitle1 separator">客服Line ID: lotto888</span>
        </div>
        <div class="copyright text-subtitle2">
          紘騰資訊工作室 ©
          {{ new Date().getFullYear() }} 版權所有。保留所有權利。
        </div>
      </div>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { useTitleStore } from 'src/stores/title';

defineOptions({
  name: 'LoginLayout',
});

const title = useTitleStore();
</script>

<style lang="scss">
.bg-login {
  background-color: #f29f0f;
}

.footer {
  min-height: 80px;
  background-color: #eeeeee; // 淺灰背景
  color: #424242; // 深灰文字
  padding: 16px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.contact-info {
  margin-bottom: 8px;

  .separator {
    margin-left: 3rem;

    // On mobile screens, display on new line
    @media (max-width: 600px) {
      display: block;
      margin-left: 0;
      margin-top: 4px;
    }
  }
}
</style>
