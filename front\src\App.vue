<template>
  <router-view />
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import { pwaInstallService } from './services/pwaInstall';
import { versionChecker } from './services/versionChecker';

// 追蹤用戶活動狀態
const userActivityTracker = {
  isUserActive: false,
  lastActivityTime: Date.now(),
  activityTimeout: 30 * 1000, // 30秒無活動視為非活躍

  // 初始化活動追蹤
  init() {
    this.isUserActive = true;
    this.lastActivityTime = Date.now();

    // 監聽用戶活動事件
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, this.updateActivity.bind(this), true);
    });

    // 定期檢查活動狀態
    setInterval(() => {
      const now = Date.now();
      if (now - this.lastActivityTime > this.activityTimeout) {
        this.isUserActive = false;
      }
    }, 5000); // 每5秒檢查一次
  },

  updateActivity() {
    this.isUserActive = true;
    this.lastActivityTime = Date.now();
  },

  // 檢查用戶是否剛進入頁面（基於頁面可見性和活動狀態）
  isNewSession(): boolean {
    // 檢查頁面是否剛從隱藏狀態變為可見
    const wasHidden = sessionStorage.getItem('page-was-hidden') === 'true';
    const isFirstLoad = !sessionStorage.getItem('app-session-started');

    // 只有當頁面從隱藏狀態變為可見時才算新會話，第一次載入不算
    return !isFirstLoad && wasHidden;
  },

  markSessionStarted() {
    sessionStorage.setItem('app-session-started', 'true');
    sessionStorage.removeItem('page-was-hidden');
  }
};

// 更新管理器 - 統一處理所有更新相關邏輯
const updateManager = {
  init() {
    // 監聽頁面可見性變化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 頁面隱藏時標記
        sessionStorage.setItem('page-was-hidden', 'true');
      } else {
        // 如果用戶重新進入頁面，檢查是否有待處理的更新
        if (sessionStorage.getItem('page-was-hidden') === 'true') {
          setTimeout(() => {
            this.checkForPendingUpdates();
          }, 1000); // 延遲1秒檢查
        }
      }
    });

    // 監聽頁面卸載
    window.addEventListener('beforeunload', () => {
      sessionStorage.setItem('page-was-hidden', 'true');
    });

    // 監聽版本檢查事件
    window.addEventListener('version-update-detected', () => {
      this.handleVersionUpdate();
    });
  },

  async checkForPendingUpdates() {
    try {
      const hasUpdate = await versionChecker.manualVersionCheck();
      if (hasUpdate) {
        // 用戶重新進入頁面時自動更新
        this.performSilentUpdate();
      }
    } catch (error) {
      console.error('檢查待處理更新失敗:', error);
    }
  },

  // 處理版本更新
  handleVersionUpdate() {
    // 檢查是否為第一次載入
    const isFirstLoad = !sessionStorage.getItem('app-session-started');
    if (isFirstLoad) {
      console.log('UpdateManager: 第一次載入，忽略版本更新檢查');
      return;
    }

    // 檢查用戶是否是新會話（從隱藏狀態返回）
    const isNewSession = userActivityTracker.isNewSession();
    const isUserActive = userActivityTracker.isUserActive;

    if (isNewSession) {
      // 新會話：自動更新
      this.performSilentUpdate();
    } else if (isUserActive) {
      // 用戶正在活躍使用：顯示更新對話框
      this.showUpdateDialog();
    } else {
      // 用戶不活躍：延遲一段時間後自動更新
      setTimeout(() => {
        if (!userActivityTracker.isUserActive) {
          this.performSilentUpdate();
        } else {
          this.showUpdateDialog();
        }
      }, 10000); // 10秒後檢查
    }
  },

  performSilentUpdate() {
    // 顯示簡短的更新提示
    this.showUpdateNotification();

    // 延遲2秒後執行更新
    setTimeout(async () => {
      try {
        await versionChecker.triggerUpdate();
      } catch (error) {
        console.error('自動更新失敗:', error);
        // 如果更新失敗，直接刷新頁面
        window.location.reload();
      }
    }, 2000);
  },

  showUpdateNotification() {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #1976d2;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      animation: slideIn 0.3s ease-out;
    `;

    notification.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <div style="
          width: 16px;
          height: 16px;
          border: 2px solid white;
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
        <span>正在更新到最新版本...</span>
      </div>
      <style>
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;

    document.body.appendChild(notification);

    // 2秒後移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 2000);
  },

  showUpdateDialog() {
    // 檢查是否已經有對話框存在，避免重複顯示
    if (document.querySelector('.update-dialog')) {
      return;
    }

    const dialog = document.createElement('div');
    dialog.className = 'update-dialog';
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      max-width: 420px;
      text-align: center;
      animation: dialogSlideIn 0.3s ease-out;
    `;

    dialogContent.innerHTML = `
      <div style="margin-bottom: 20px;">
        <div style="font-size: 20px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          🎉 發現新版本！
        </div>
        <div style="color: #666; font-size: 15px; line-height: 1.4;">
          應用程式有新版本可用，包含功能改進和錯誤修復。<br>
          <small style="color: #999;">為避免中斷您的操作，您可以選擇更新時機</small>
        </div>
      </div>
      <div style="display: flex; flex-direction: column; gap: 8px;">
        <button id="update-now" style="
          padding: 12px 20px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 6px;
          cursor: pointer;
          font-size: 15px;
          font-weight: 500;
          transition: background 0.2s;
        ">立即更新</button>
        <div style="display: flex; gap: 8px;">
          <button id="update-later" style="
            flex: 1;
            padding: 10px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">稍後提醒 (30分鐘)</button>
          <button id="update-next-visit" style="
            flex: 1;
            padding: 10px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">下次進入時更新</button>
        </div>
      </div>
      <style>
        @keyframes dialogSlideIn {
          from { transform: scale(0.9) translateY(-20px); opacity: 0; }
          to { transform: scale(1) translateY(0); opacity: 1; }
        }
        #update-now:hover { background: #1565c0; }
        #update-later:hover, #update-next-visit:hover { background: #f5f5f5; }
      </style>
    `;

    dialog.appendChild(dialogContent);
    document.body.appendChild(dialog);

    // 綁定按鈕事件
    const updateNowBtn = dialog.querySelector('#update-now');
    const updateLaterBtn = dialog.querySelector('#update-later');
    const updateNextVisitBtn = dialog.querySelector('#update-next-visit');

    updateNowBtn?.addEventListener('click', async () => {
      document.body.removeChild(dialog);
      try {
        await versionChecker.triggerUpdate();
      } catch (error) {
        console.error('更新失敗:', error);
        // 如果更新失敗，直接刷新頁面
        window.location.reload();
      }
    });

    updateLaterBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      setTimeout(() => {
        this.showUpdateDialog();
      }, 30 * 60 * 1000); // 30分鐘
    });

    updateNextVisitBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      // 設置標記，下次進入時自動更新
      sessionStorage.setItem('pending-update', 'true');
    });
  }
};

// 在應用啟動時初始化
onMounted(() => {
  // 初始化用戶活動追蹤
  userActivityTracker.init();

  // 初始化更新管理器
  updateManager.init();

  // 檢查是否有待處理的更新
  if (sessionStorage.getItem('pending-update') === 'true') {
    sessionStorage.removeItem('pending-update');
    setTimeout(() => {
      updateManager.performSilentUpdate();
    }, 1000);
  }

  // 標記會話已開始
  userActivityTracker.markSessionStarted();

  // 初始化 PWA 安裝服務
  pwaInstallService.initialize();

  // 啟動版本檢查器
  versionChecker.startVersionCheck();

  // 監聽來自 Service Worker 的消息
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && (event.data.type === 'RELOAD_PAGE' || event.data.type === 'FORCE_RELOAD')) {
        // 檢查是否為第一次載入
        const isFirstLoad = !sessionStorage.getItem('app-session-started');
        if (isFirstLoad) {
          console.log('ServiceWorker: 第一次載入，忽略更新消息');
          return;
        }

        // 根據用戶狀態決定如何處理
        if (userActivityTracker.isUserActive) {
          updateManager.showUpdateDialog();
        } else {
          updateManager.performSilentUpdate();
        }
      }
    });

    // 監聽 Service Worker 狀態變化
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      // 檢查是否為第一次載入
      const isFirstLoad = !sessionStorage.getItem('app-session-started');
      if (isFirstLoad) {
        console.log('ServiceWorker: 第一次載入，忽略控制器變化');
        return;
      }

      // 同樣根據用戶狀態決定處理方式
      if (userActivityTracker.isUserActive) {
        updateManager.showUpdateDialog();
      } else {
        updateManager.performSilentUpdate();
      }
    });
  }
});

// 清理資源
onUnmounted(() => {
  versionChecker.destroy();
});

defineOptions({
  name: 'App',
});
</script>
